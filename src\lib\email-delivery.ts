import { prisma } from './prisma'
import { 
  EmailMessage, 
  EmailAttachment,
  generateMessageId, 
  generateThreadId,
  isInternalEmail,
  validateEmailAddress,
  checkStorageQuota,
  updateStorageUsage,
  checkRateLimit,
  getFolderByType,
  processAttachments,
  applySpamFilters,
  calculateEmailSize,
  generateEmailHeaders,
  createSMTPTransporter,
  EmailServerError,
  QuotaExceededError,
  RateLimitError,
  InvalidEmailError
} from './email-server'

// Send email function - handles both internal and external delivery
export async function sendEmail(
  senderAccountId: string,
  emailMessage: EmailMessage
): Promise<{ success: boolean; messageId: string; error?: string }> {
  try {
    // Validate sender account
    const senderAccount = await prisma.emailAccount.findUnique({
      where: { id: senderAccountId, isActive: true }
    })

    if (!senderAccount) {
      throw new EmailServerError('Sender account not found or inactive', 'INVALID_SENDER', 404)
    }

    // Check rate limiting
    const rateLimitOk = await checkRateLimit(senderAccountId)
    if (!rateLimitOk) {
      throw new RateLimitError('Email rate limit exceeded')
    }

    // Validate email addresses
    const allEmails = [
      ...emailMessage.toEmails,
      ...(emailMessage.ccEmails || []),
      ...(emailMessage.bccEmails || [])
    ]

    for (const email of allEmails) {
      if (!validateEmailAddress(email)) {
        throw new InvalidEmailError(`Invalid email address: ${email}`)
      }
    }

    // Generate message ID and thread ID
    const messageId = emailMessage.messageId || generateMessageId()
    const threadId = emailMessage.inReplyTo ? 
      await getThreadIdFromReplyTo(emailMessage.inReplyTo) : 
      generateThreadId()

    // Calculate email size
    const emailSize = calculateEmailSize(emailMessage, emailMessage.attachments || [])

    // Check storage quota
    const quotaOk = await checkStorageQuota(senderAccountId, emailSize)
    if (!quotaOk) {
      throw new QuotaExceededError('Storage quota exceeded')
    }

    // Apply spam filters
    const { isSpam, action } = await applySpamFilters(emailMessage)

    // Create email record
    const email = await prisma.email.create({
      data: {
        messageId,
        subject: emailMessage.subject,
        body: emailMessage.body,
        bodyText: emailMessage.bodyText,
        fromEmail: emailMessage.fromEmail,
        fromName: emailMessage.fromName,
        senderId: senderAccountId,
        priority: emailMessage.priority || 'NORMAL',
        isSpam,
        isDraft: emailMessage.isDraft || false,
        threadId,
        inReplyTo: emailMessage.inReplyTo,
        references: emailMessage.references,
        sentAt: emailMessage.isDraft ? null : new Date(),
      }
    })

    // Process attachments if any
    let attachmentSize = 0
    if (emailMessage.attachments && emailMessage.attachments.length > 0) {
      const { success, totalSize } = await processAttachments(email.id, emailMessage.attachments)
      if (!success) {
        throw new EmailServerError('Failed to process attachments', 'ATTACHMENT_ERROR')
      }
      attachmentSize = totalSize
    }

    // Update storage usage
    await updateStorageUsage(senderAccountId, emailSize)

    // Create recipients and handle delivery
    await createEmailRecipients(email.id, emailMessage, isSpam)

    // Add to sender's SENT folder (if not draft)
    if (!emailMessage.isDraft) {
      const sentFolder = await getFolderByType(senderAccountId, 'SENT')
      if (sentFolder) {
        await prisma.emailRecipient.create({
          data: {
            emailId: email.id,
            accountId: senderAccountId,
            recipientType: 'TO',
            folderId: sentFolder.id,
          }
        })
      }

      // Queue for external delivery
      await queueExternalEmails(email.id, emailMessage)
    }

    return { success: true, messageId }

  } catch (error) {
    console.error('Error sending email:', error)
    
    if (error instanceof EmailServerError) {
      return { success: false, messageId: '', error: error.message }
    }
    
    return { success: false, messageId: '', error: 'Failed to send email' }
  }
}

// Create email recipients for internal delivery
async function createEmailRecipients(
  emailId: string,
  emailMessage: EmailMessage,
  isSpam: boolean
): Promise<void> {
  const recipientTypes = [
    { emails: emailMessage.toEmails, type: 'TO' as const },
    { emails: emailMessage.ccEmails || [], type: 'CC' as const },
    { emails: emailMessage.bccEmails || [], type: 'BCC' as const },
  ]

  for (const { emails, type } of recipientTypes) {
    for (const email of emails) {
      if (isInternalEmail(email)) {
        // Find internal recipient account
        const recipientAccount = await prisma.emailAccount.findUnique({
          where: { email, isActive: true }
        })

        if (recipientAccount) {
          // Determine target folder
          const targetFolder = isSpam ? 
            await getFolderByType(recipientAccount.id, 'SPAM') :
            await getFolderByType(recipientAccount.id, 'INBOX')

          // Create recipient record
          await prisma.emailRecipient.create({
            data: {
              emailId,
              accountId: recipientAccount.id,
              recipientType: type,
              folderId: targetFolder?.id,
            }
          })

          // Update recipient's storage usage
          const emailSize = await calculateEmailSizeFromDb(emailId)
          await updateStorageUsage(recipientAccount.id, emailSize)
        }
      }
    }
  }
}

// Queue external emails for delivery
async function queueExternalEmails(emailId: string, emailMessage: EmailMessage): Promise<void> {
  const externalEmails = [
    ...emailMessage.toEmails,
    ...(emailMessage.ccEmails || []),
    ...(emailMessage.bccEmails || [])
  ].filter(email => !isInternalEmail(email))

  for (const email of externalEmails) {
    await prisma.emailQueue.create({
      data: {
        emailId,
        recipientEmail: email,
        priority: emailMessage.priority === 'URGENT' ? 1 : 
                 emailMessage.priority === 'HIGH' ? 2 : 5,
        status: 'PENDING',
        scheduledAt: new Date(),
      }
    })
  }
}

// Process email queue for external delivery
export async function processEmailQueue(): Promise<void> {
  const queueItems = await prisma.emailQueue.findMany({
    where: {
      status: 'PENDING',
      scheduledAt: { lte: new Date() },
      attempts: { lt: 3 }
    },
    include: {
      email: {
        include: {
          attachments: true,
          sender: true
        }
      }
    },
    orderBy: [
      { priority: 'asc' },
      { scheduledAt: 'asc' }
    ],
    take: 10 // Process 10 emails at a time
  })

  for (const queueItem of queueItems) {
    await deliverExternalEmail(queueItem)
  }
}

// Deliver email to external recipient
async function deliverExternalEmail(queueItem: any): Promise<void> {
  try {
    // Update queue item to processing
    await prisma.emailQueue.update({
      where: { id: queueItem.id },
      data: { 
        status: 'PROCESSING',
        attempts: { increment: 1 }
      }
    })

    const transporter = createSMTPTransporter()
    const email = queueItem.email

    // Prepare email for external delivery
    const mailOptions = {
      from: `${email.fromName || email.sender.displayName} <${email.fromEmail}>`,
      to: queueItem.recipientEmail,
      subject: email.subject,
      html: email.body,
      text: email.bodyText,
      messageId: email.messageId,
      inReplyTo: email.inReplyTo,
      references: email.references,
      attachments: email.attachments.map((att: any) => ({
        filename: att.filename,
        path: att.url,
        contentType: att.mimeType
      }))
    }

    // Send email
    const info = await transporter.sendMail(mailOptions)

    // Update queue item to sent
    await prisma.emailQueue.update({
      where: { id: queueItem.id },
      data: { 
        status: 'SENT',
        processedAt: new Date()
      }
    })

    // Log delivery
    await prisma.emailDeliveryLog.create({
      data: {
        emailId: email.id,
        recipientEmail: queueItem.recipientEmail,
        status: 'SENT',
        externalId: info.messageId
      }
    })

    console.log(`Email delivered to ${queueItem.recipientEmail}: ${info.messageId}`)

  } catch (error) {
    console.error(`Failed to deliver email to ${queueItem.recipientEmail}:`, error)

    // Calculate next retry time (exponential backoff)
    const nextRetry = new Date(Date.now() + Math.pow(2, queueItem.attempts) * 60000) // 1min, 2min, 4min

    // Update queue item with error
    await prisma.emailQueue.update({
      where: { id: queueItem.id },
      data: {
        status: queueItem.attempts >= 3 ? 'FAILED' : 'PENDING',
        nextRetry: queueItem.attempts < 3 ? nextRetry : null,
        lastError: error instanceof Error ? error.message : 'Unknown error'
      }
    })

    // Log delivery failure
    await prisma.emailDeliveryLog.create({
      data: {
        emailId: queueItem.email.id,
        recipientEmail: queueItem.recipientEmail,
        status: queueItem.attempts >= 3 ? 'FAILED' : 'DEFERRED',
        errorMessage: error instanceof Error ? error.message : 'Unknown error'
      }
    })
  }
}

// Get thread ID from reply-to message
async function getThreadIdFromReplyTo(inReplyTo: string): Promise<string> {
  const originalEmail = await prisma.email.findUnique({
    where: { messageId: inReplyTo },
    select: { threadId: true }
  })

  return originalEmail?.threadId || generateThreadId()
}

// Calculate email size from database record
async function calculateEmailSizeFromDb(emailId: string): Promise<number> {
  const email = await prisma.email.findUnique({
    where: { id: emailId },
    include: { attachments: true }
  })

  if (!email) return 0

  const bodySize = Buffer.byteLength(email.body, 'utf8')
  const bodyTextSize = email.bodyText ? Buffer.byteLength(email.bodyText, 'utf8') : 0
  const subjectSize = Buffer.byteLength(email.subject, 'utf8')
  const attachmentSize = email.attachments.reduce((total, att) => total + att.size, 0)

  return bodySize + bodyTextSize + subjectSize + attachmentSize
}

// Save email as draft
export async function saveDraft(
  senderAccountId: string,
  emailMessage: EmailMessage
): Promise<{ success: boolean; draftId: string; error?: string }> {
  try {
    emailMessage.isDraft = true
    const result = await sendEmail(senderAccountId, emailMessage)
    
    if (result.success) {
      // Move to drafts folder
      const email = await prisma.email.findUnique({
        where: { messageId: result.messageId }
      })

      if (email) {
        const draftsFolder = await getFolderByType(senderAccountId, 'DRAFTS')
        if (draftsFolder) {
          await prisma.emailRecipient.create({
            data: {
              emailId: email.id,
              accountId: senderAccountId,
              recipientType: 'TO',
              folderId: draftsFolder.id,
            }
          })
        }
      }

      return { success: true, draftId: email?.id || '' }
    }

    return { success: false, draftId: '', error: result.error }
  } catch (error) {
    console.error('Error saving draft:', error)
    return { success: false, draftId: '', error: 'Failed to save draft' }
  }
}
