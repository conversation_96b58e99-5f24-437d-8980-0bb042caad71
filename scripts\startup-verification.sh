#!/bin/bash

echo "========================================"
echo "EMAIL SERVER STARTUP VERIFICATION"
echo "========================================"
echo

echo "[1/5] Checking Environment Setup..."
if [ ! -f .env ]; then
    echo "❌ ERROR: .env file not found!"
    echo "Please ensure .env file exists with required variables."
    exit 1
fi
echo "✅ Environment file found"

echo
echo "[2/5] Generating Prisma Client..."
npx prisma generate
if [ $? -ne 0 ]; then
    echo "❌ ERROR: Prisma client generation failed!"
    exit 1
fi
echo "✅ Prisma client generated successfully"

echo
echo "[3/5] Setting up Database..."
npx prisma db push
if [ $? -ne 0 ]; then
    echo "❌ ERROR: Database setup failed!"
    exit 1
fi
echo "✅ Database schema created successfully"

echo
echo "[4/5] Checking Dependencies..."
npm list --depth=0 > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "⚠️  WARNING: Some dependencies may be missing"
    echo "Running npm install..."
    npm install
fi
echo "✅ Dependencies verified"

echo
echo "[5/5] Starting Development Server..."
echo
echo "========================================"
echo "STARTING EMAIL SERVER ON PORT 3000"
echo "========================================"
echo
echo "Student Portal: http://localhost:3000/student/login"
echo "Admin Portal:   http://localhost:3000/admin/login"
echo
echo "Press Ctrl+C to stop the server"
echo "========================================"
echo

npm run dev
