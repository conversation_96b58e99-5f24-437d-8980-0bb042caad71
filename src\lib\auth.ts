/* eslint-disable @typescript-eslint/no-explicit-any */
import Credentials<PERSON>rovider from "next-auth/providers/credentials"
import bcrypt from "bcryptjs"
import { prisma } from "./prisma"
import type { NextAuthOptions } from "next-auth"

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      id: "admin-credentials",
      name: "Admin Login",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        // Skip authentication during build time
        if (process.env.NODE_ENV === 'production' && process.env.VERCEL_ENV === 'production' && !prisma) {
          return null
        }

        try {
          const user = await prisma.user.findUnique({
            where: {
              email: credentials.email
            }
          })

          if (!user) {
            return null
          }

          const isPasswordValid = await bcrypt.compare(
            credentials.password,
            user.password
          )

          if (!isPasswordValid) {
            return null
          }

          return {
            id: user.id,
            email: user.email,
            name: user.name,
            role: user.role,
            type: 'admin'
          }
        } catch (error) {
          if (process.env.NODE_ENV === 'development') {
            console.error("Admin authentication error:", error)
          }
          return null
        }
      }
    }),
    CredentialsProvider({
      id: "student-credentials",
      name: "Student Login",
      credentials: {
        studentId: { label: "Student ID", type: "text" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.studentId || !credentials?.password) {
          return null
        }

        // Skip authentication during build time
        if (process.env.NODE_ENV === 'production' && process.env.VERCEL_ENV === 'production' && !prisma) {
          return null
        }

        try {
          const account = await prisma.emailAccount.findUnique({
            where: {
              studentId: credentials.studentId,
              accountType: 'STUDENT_ID',
              isActive: true
            }
          })

          if (!account) {
            return null
          }

          const isPasswordValid = await bcrypt.compare(
            credentials.password,
            account.password
          )

          if (!isPasswordValid) {
            return null
          }

          return {
            id: account.id,
            email: account.email,
            name: account.displayName || account.studentId,
            studentId: account.studentId,
            role: 'STUDENT',
            type: 'student'
          }
        } catch (error) {
          if (process.env.NODE_ENV === 'development') {
            console.error("Student authentication error:", error)
          }
          return null
        }
      }
    })
  ],
  session: {
    strategy: "jwt" as const,
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  callbacks: {
    async jwt({ token, user }: { token: any; user: any }) {
      if (user) {
        token.role = user.role
        token.id = user.id
        token.type = user.type
        if (user.studentId) {
          token.studentId = user.studentId
        }
      }
      return token
    },
    async session({ session, token }: { session: any; token: any }) {
      if (token) {
        session.user.id = token.id || token.sub!
        session.user.role = token.role as string
        session.user.type = token.type as string
        if (token.studentId) {
          session.user.studentId = token.studentId as string
        }
      }
      return session
    },
    async redirect({ url, baseUrl }: { url: string; baseUrl: string }) {
      // Allows relative callback URLs
      if (url.startsWith("/")) return `${baseUrl}${url}`
      // Allows callback URLs on the same origin
      else if (new URL(url).origin === baseUrl) return url
      return baseUrl
    }
  },
  pages: {
    signIn: "/admin/login",
  },
  secret: process.env.NEXTAUTH_SECRET,
  debug: false,
}
