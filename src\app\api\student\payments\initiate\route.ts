import { NextRequest, NextResponse } from 'next/server'
import { createSecure<PERSON>pi } from '@/lib/secure-api'
import { prisma } from '@/lib/prisma'
import jwt from 'jsonwebtoken'

// POST /api/student/payments/initiate - Initiate payment for student
export const POST = createSecureApi(
  async (context) => {
    try {
      // Get student from token
      const authHeader = context.request.headers.get('authorization')
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return NextResponse.json(
          { error: 'No token provided' },
          { status: 401 }
        )
      }

      const token = authHeader.substring(7)
      const decoded = jwt.verify(
        token,
        process.env.JWT_SECRET || 'fallback-secret'
      ) as any

      if (decoded.type !== 'student') {
        return NextResponse.json(
          { error: 'Invalid token type' },
          { status: 401 }
        )
      }

      const studentId = decoded.studentId
      const accountId = decoded.accountId
      const body = await context.request.json()
      const { paymentId, gateway } = body

      if (!paymentId || !gateway) {
        return NextResponse.json(
          { error: 'Payment ID and gateway are required' },
          { status: 400 }
        )
      }

      // Get gateway configuration
      const gatewayConfig = await prisma.paymentGatewayConfig.findUnique({
        where: { 
          gateway: gateway as any,
          isEnabled: true 
        }
      })

      if (!gatewayConfig) {
        return NextResponse.json(
          { error: 'Payment gateway not available' },
          { status: 400 }
        )
      }

      // Get student account details
      const student = await prisma.emailAccount.findUnique({
        where: { id: accountId },
        select: {
          email: true,
          displayName: true,
          studentId: true
        }
      })

      if (!student) {
        return NextResponse.json(
          { error: 'Student not found' },
          { status: 404 }
        )
      }

      // For demo purposes, we'll simulate payment initiation
      // In a real implementation, you would:
      // 1. Get the actual payment details from database
      // 2. Calculate total amount with gateway fees
      // 3. Create payment request with the gateway
      // 4. Return the gateway's redirect URL

      // Sample payment data (this would come from database)
      const samplePayments: Record<string, any> = {
        'pay_001': {
          description: 'Tuition Fee - Semester 1',
          amount: 50000,
          category: 'TUITION'
        },
        'pay_002': {
          description: 'Library Fee',
          amount: 2000,
          category: 'LIBRARY'
        },
        'pay_003': {
          description: 'Hostel Fee - Semester 1',
          amount: 25000,
          category: 'HOSTEL'
        },
        'pay_006': {
          description: 'Lab Fee - Computer Science',
          amount: 5000,
          category: 'OTHER'
        }
      }

      const payment = samplePayments[paymentId]
      if (!payment) {
        return NextResponse.json(
          { error: 'Payment not found' },
          { status: 404 }
        )
      }

      // Calculate total amount with gateway fees
      const baseAmount = payment.amount
      const percentageFee = (baseAmount * gatewayConfig.additionalFeePercent) / 100
      const totalAmount = baseAmount + percentageFee + gatewayConfig.additionalFeeFixed

      // Generate unique transaction ID
      const transactionId = `TXN_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

      // Create payment record (in real implementation)
      // await prisma.paymentTransaction.create({
      //   data: {
      //     id: transactionId,
      //     studentId,
      //     paymentId,
      //     gateway: gatewayConfig.gateway,
      //     baseAmount,
      //     gatewayFee: percentageFee + gatewayConfig.additionalFeeFixed,
      //     totalAmount,
      //     status: 'INITIATED',
      //     // ... other fields
      //   }
      // })

      // Simulate gateway-specific payment initiation
      let redirectUrl: string

      switch (gateway) {
        case 'PAYU':
          redirectUrl = await initiatePayUPayment({
            transactionId,
            amount: totalAmount,
            studentEmail: student.email,
            studentName: student.displayName || student.studentId,
            description: payment.description,
            isTestMode: gatewayConfig.isTestMode
          })
          break

        case 'PHONEPE':
          redirectUrl = await initiatePhonePePayment({
            transactionId,
            amount: totalAmount,
            studentEmail: student.email,
            studentName: student.displayName || student.studentId,
            description: payment.description,
            isTestMode: gatewayConfig.isTestMode
          })
          break

        case 'CASHFREE':
          redirectUrl = await initiateCashfreePayment({
            transactionId,
            amount: totalAmount,
            studentEmail: student.email,
            studentName: student.displayName || student.studentId,
            description: payment.description,
            isTestMode: gatewayConfig.isTestMode
          })
          break

        default:
          return NextResponse.json(
            { error: 'Unsupported payment gateway' },
            { status: 400 }
          )
      }

      return NextResponse.json({
        success: true,
        transactionId,
        redirectUrl,
        totalAmount,
        gateway: gatewayConfig.gateway,
        message: 'Payment initiated successfully'
      })

    } catch (error) {
      console.error('Payment initiation error:', error)
      return NextResponse.json(
        { error: 'Failed to initiate payment' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: false, // We handle auth manually
    logAudit: true,
    sanitizeInput: true
  }
)

// Gateway-specific payment initiation functions
async function initiatePayUPayment(params: any): Promise<string> {
  // In a real implementation, you would:
  // 1. Create PayU payment request
  // 2. Generate hash using merchant key and salt
  // 3. Return PayU payment URL
  
  // For demo, return a mock URL
  return `https://test.payu.in/payment?txnid=${params.transactionId}&amount=${params.amount}&email=${params.studentEmail}`
}

async function initiatePhonePePayment(params: any): Promise<string> {
  // In a real implementation, you would:
  // 1. Create PhonePe payment request
  // 2. Generate X-VERIFY header
  // 3. Return PhonePe payment URL
  
  // For demo, return a mock URL
  return `https://mercury-uat.phonepe.com/transact?txnid=${params.transactionId}&amount=${params.amount}`
}

async function initiateCashfreePayment(params: any): Promise<string> {
  // In a real implementation, you would:
  // 1. Create Cashfree payment session
  // 2. Generate payment link
  // 3. Return Cashfree payment URL
  
  // For demo, return a mock URL
  return `https://test.cashfree.com/billpay/checkout?order_id=${params.transactionId}&amount=${params.amount}`
}
