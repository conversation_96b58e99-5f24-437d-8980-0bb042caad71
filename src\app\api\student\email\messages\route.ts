import { NextRequest, NextResponse } from 'next/server'
import { createSecure<PERSON><PERSON> } from '@/lib/secure-api'
import { prisma } from '@/lib/prisma'
import jwt from 'jsonwebtoken'

// GET /api/student/email/messages - Get emails for student
export const GET = createSecureApi(
  async (context) => {
    try {
      // Get student from token
      const authHeader = context.request.headers.get('authorization')
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return NextResponse.json(
          { error: 'No token provided' },
          { status: 401 }
        )
      }

      const token = authHeader.substring(7)
      const decoded = jwt.verify(
        token,
        process.env.JWT_SECRET || 'fallback-secret'
      ) as any

      if (decoded.type !== 'student') {
        return NextResponse.json(
          { error: 'Invalid token type' },
          { status: 401 }
        )
      }

      const accountId = decoded.accountId
      const { searchParams } = new URL(context.request.url)
      
      const page = parseInt(searchParams.get('page') || '1')
      const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 100)
      const folderType = searchParams.get('folderType') || 'INBOX'
      const search = searchParams.get('search')
      const unreadOnly = searchParams.get('unreadOnly') === 'true'
      const starredOnly = searchParams.get('starredOnly') === 'true'
      const hasAttachments = searchParams.get('hasAttachments') === 'true'

      const skip = (page - 1) * limit

      // Build where clause
      const whereClause: any = {
        accountId,
        isDeleted: false,
        folder: {
          folderType
        }
      }

      // Apply filters
      if (unreadOnly) {
        whereClause.isRead = false
      }

      if (starredOnly) {
        whereClause.email = {
          ...whereClause.email,
          isStarred: true
        }
      }

      if (hasAttachments) {
        whereClause.email = {
          ...whereClause.email,
          attachments: {
            some: {}
          }
        }
      }

      if (search) {
        whereClause.email = {
          ...whereClause.email,
          OR: [
            { subject: { contains: search, mode: 'insensitive' } },
            { body: { contains: search, mode: 'insensitive' } },
            { fromEmail: { contains: search, mode: 'insensitive' } },
            { fromName: { contains: search, mode: 'insensitive' } }
          ]
        }
      }

      // Get emails with pagination
      const [emailRecipients, totalCount] = await Promise.all([
        prisma.emailRecipient.findMany({
          where: whereClause,
          include: {
            email: {
              include: {
                attachments: {
                  select: {
                    id: true,
                    filename: true,
                    size: true
                  }
                }
              }
            },
            folder: {
              select: {
                name: true,
                folderType: true
              }
            }
          },
          orderBy: {
            email: {
              sentAt: 'desc'
            }
          },
          skip,
          take: limit
        }),

        prisma.emailRecipient.count({
          where: whereClause
        })
      ])

      // Transform to client format
      const messages = emailRecipients.map(recipient => {
        const email = recipient.email
        
        // Generate preview text
        const preview = email.bodyText ? 
          email.bodyText.substring(0, 200) + (email.bodyText.length > 200 ? '...' : '') :
          email.body.replace(/<[^>]*>/g, '').substring(0, 200) + '...'

        return {
          id: email.id,
          messageId: email.messageId,
          subject: email.subject,
          fromEmail: email.fromEmail,
          fromName: email.fromName,
          receivedAt: email.sentAt,
          isRead: recipient.isRead,
          isStarred: email.isStarred,
          hasAttachments: email.attachments.length > 0,
          attachmentCount: email.attachments.length,
          priority: email.priority,
          preview,
          folder: recipient.folder?.name || 'Unknown'
        }
      })

      return NextResponse.json({
        success: true,
        messages,
        pagination: {
          page,
          limit,
          totalCount,
          totalPages: Math.ceil(totalCount / limit),
          hasMore: skip + messages.length < totalCount
        }
      })

    } catch (error) {
      console.error('Student email messages error:', error)
      return NextResponse.json(
        { error: 'Failed to retrieve emails' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: false, // We handle auth manually
    logAudit: false
  }
)

// PATCH /api/student/email/messages - Update email properties
export const PATCH = createSecureApi(
  async (context) => {
    try {
      // Get student from token
      const authHeader = context.request.headers.get('authorization')
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return NextResponse.json(
          { error: 'No token provided' },
          { status: 401 }
        )
      }

      const token = authHeader.substring(7)
      const decoded = jwt.verify(
        token,
        process.env.JWT_SECRET || 'fallback-secret'
      ) as any

      if (decoded.type !== 'student') {
        return NextResponse.json(
          { error: 'Invalid token type' },
          { status: 401 }
        )
      }

      const accountId = decoded.accountId
      const body = await context.request.json()
      const { emailId, action, value } = body

      if (!emailId || !action) {
        return NextResponse.json(
          { error: 'Email ID and action are required' },
          { status: 400 }
        )
      }

      // Verify the email belongs to this student
      const emailRecipient = await prisma.emailRecipient.findFirst({
        where: {
          accountId,
          email: {
            id: emailId
          }
        }
      })

      if (!emailRecipient) {
        return NextResponse.json(
          { error: 'Email not found' },
          { status: 404 }
        )
      }

      // Perform the requested action
      switch (action) {
        case 'markRead':
          await prisma.emailRecipient.update({
            where: { id: emailRecipient.id },
            data: { isRead: value }
          })
          break

        case 'star':
          await prisma.email.update({
            where: { id: emailId },
            data: { isStarred: value }
          })
          break

        case 'move':
          // Move to different folder
          const targetFolder = await prisma.emailFolder.findFirst({
            where: {
              accountId,
              id: value
            }
          })

          if (!targetFolder) {
            return NextResponse.json(
              { error: 'Target folder not found' },
              { status: 404 }
            )
          }

          await prisma.emailRecipient.update({
            where: { id: emailRecipient.id },
            data: { folderId: value }
          })
          break

        default:
          return NextResponse.json(
            { error: 'Invalid action' },
            { status: 400 }
          )
      }

      return NextResponse.json({
        success: true,
        message: 'Email updated successfully'
      })

    } catch (error) {
      console.error('Student email update error:', error)
      return NextResponse.json(
        { error: 'Failed to update email' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: false, // We handle auth manually
    logAudit: true,
    sanitizeInput: true
  }
)
