'use client'

import { useState, useRef } from 'react'
import { useRouter } from 'next/navigation'
import StudentLayout from '@/components/student/student-layout'
import { 
  Send, 
  Paperclip, 
  Save, 
  X, 
  Plus,
  AlertCircle,
  CheckCircle,
  Upload,
  Trash2
} from 'lucide-react'

interface Attachment {
  id: string
  file: File
  uploading: boolean
  uploaded: boolean
  error?: string
}

interface EmailForm {
  to: string
  cc: string
  bcc: string
  subject: string
  body: string
  priority: 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT'
}

export default function ComposeEmailPage() {
  const router = useRouter()
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [formData, setFormData] = useState<EmailForm>({
    to: '',
    cc: '',
    bcc: '',
    subject: '',
    body: '',
    priority: 'NORMAL'
  })
  const [attachments, setAttachments] = useState<Attachment[]>([])
  const [showCcBcc, setShowCcBcc] = useState(false)
  const [sending, setSending] = useState(false)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    
    files.forEach(file => {
      // Check file size (10MB limit)
      if (file.size > 10 * 1024 * 1024) {
        setError(`File ${file.name} is too large. Maximum size is 10MB.`)
        return
      }

      const attachment: Attachment = {
        id: Math.random().toString(36).substr(2, 9),
        file,
        uploading: false,
        uploaded: false
      }

      setAttachments(prev => [...prev, attachment])
    })

    // Clear the input
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const removeAttachment = (attachmentId: string) => {
    setAttachments(prev => prev.filter(att => att.id !== attachmentId))
  }

  const uploadAttachments = async (): Promise<string[]> => {
    const uploadedAttachments: string[] = []

    for (const attachment of attachments) {
      if (attachment.uploaded) continue

      try {
        // Update attachment status
        setAttachments(prev => prev.map(att => 
          att.id === attachment.id ? { ...att, uploading: true } : att
        ))

        const formData = new FormData()
        formData.append('file', attachment.file)

        const token = localStorage.getItem('student_token')
        const response = await fetch('/api/student/email/attachments/upload', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`
          },
          body: formData
        })

        if (!response.ok) {
          throw new Error('Upload failed')
        }

        const data = await response.json()
        uploadedAttachments.push(data.attachmentId)

        // Update attachment status
        setAttachments(prev => prev.map(att => 
          att.id === attachment.id ? { ...att, uploading: false, uploaded: true } : att
        ))

      } catch (error) {
        setAttachments(prev => prev.map(att => 
          att.id === attachment.id ? { 
            ...att, 
            uploading: false, 
            error: 'Upload failed' 
          } : att
        ))
      }
    }

    return uploadedAttachments
  }

  const handleSend = async () => {
    setError('')
    setSuccess('')

    // Validation
    if (!formData.to.trim()) {
      setError('Please enter at least one recipient')
      return
    }

    if (!formData.subject.trim()) {
      setError('Please enter a subject')
      return
    }

    if (!formData.body.trim()) {
      setError('Please enter a message')
      return
    }

    try {
      setSending(true)

      // Upload attachments first
      const attachmentIds = await uploadAttachments()

      // Send email
      const token = localStorage.getItem('student_token')
      const response = await fetch('/api/student/email/send', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          to: formData.to.split(',').map(email => email.trim()).filter(Boolean),
          cc: formData.cc ? formData.cc.split(',').map(email => email.trim()).filter(Boolean) : [],
          bcc: formData.bcc ? formData.bcc.split(',').map(email => email.trim()).filter(Boolean) : [],
          subject: formData.subject,
          body: formData.body,
          priority: formData.priority,
          attachments: attachmentIds
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to send email')
      }

      setSuccess('Email sent successfully!')
      
      // Clear form after successful send
      setTimeout(() => {
        router.push('/student/email/sent')
      }, 2000)

    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to send email')
    } finally {
      setSending(false)
    }
  }

  const handleSaveDraft = async () => {
    setError('')
    setSuccess('')

    try {
      setSaving(true)

      // Upload attachments first
      const attachmentIds = await uploadAttachments()

      const token = localStorage.getItem('student_token')
      const response = await fetch('/api/student/email/drafts', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          to: formData.to.split(',').map(email => email.trim()).filter(Boolean),
          cc: formData.cc ? formData.cc.split(',').map(email => email.trim()).filter(Boolean) : [],
          bcc: formData.bcc ? formData.bcc.split(',').map(email => email.trim()).filter(Boolean) : [],
          subject: formData.subject,
          body: formData.body,
          priority: formData.priority,
          attachments: attachmentIds
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to save draft')
      }

      setSuccess('Draft saved successfully!')

    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to save draft')
    } finally {
      setSaving(false)
    }
  }

  const formatFileSize = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    if (bytes === 0) return '0 Bytes'
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
  }

  return (
    <StudentLayout>
      <div className="max-w-4xl mx-auto">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">Compose Email</h1>
          <p className="text-sm text-gray-600">Create and send a new email message</p>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6 flex items-center">
            <AlertCircle className="h-5 w-5 mr-2 flex-shrink-0" />
            {error}
          </div>
        )}

        {success && (
          <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-6 flex items-center">
            <CheckCircle className="h-5 w-5 mr-2 flex-shrink-0" />
            {success}
          </div>
        )}

        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex justify-between items-center">
              <h2 className="text-lg font-medium text-gray-900">New Message</h2>
              <div className="flex space-x-2">
                <button
                  onClick={handleSaveDraft}
                  disabled={saving}
                  className="inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                >
                  {saving ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mr-2"></div>
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Save Draft
                    </>
                  )}
                </button>
                <button
                  onClick={handleSend}
                  disabled={sending}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                >
                  {sending ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Sending...
                    </>
                  ) : (
                    <>
                      <Send className="h-4 w-4 mr-2" />
                      Send
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>

          <div className="p-6 space-y-4">
            {/* To Field */}
            <div>
              <label htmlFor="to" className="block text-sm font-medium text-gray-700">
                To *
              </label>
              <input
                type="email"
                id="to"
                name="to"
                multiple
                value={formData.to}
                onChange={handleInputChange}
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                placeholder="<EMAIL>, <EMAIL>"
              />
              <p className="mt-1 text-xs text-gray-500">
                Separate multiple email addresses with commas
              </p>
            </div>

            {/* CC/BCC Toggle */}
            {!showCcBcc && (
              <div>
                <button
                  onClick={() => setShowCcBcc(true)}
                  className="text-sm text-blue-600 hover:text-blue-800"
                >
                  + Add CC/BCC
                </button>
              </div>
            )}

            {/* CC Field */}
            {showCcBcc && (
              <div>
                <label htmlFor="cc" className="block text-sm font-medium text-gray-700">
                  CC
                </label>
                <input
                  type="email"
                  id="cc"
                  name="cc"
                  multiple
                  value={formData.cc}
                  onChange={handleInputChange}
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="<EMAIL>"
                />
              </div>
            )}

            {/* BCC Field */}
            {showCcBcc && (
              <div>
                <label htmlFor="bcc" className="block text-sm font-medium text-gray-700">
                  BCC
                </label>
                <input
                  type="email"
                  id="bcc"
                  name="bcc"
                  multiple
                  value={formData.bcc}
                  onChange={handleInputChange}
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="<EMAIL>"
                />
              </div>
            )}

            {/* Subject and Priority */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="md:col-span-3">
                <label htmlFor="subject" className="block text-sm font-medium text-gray-700">
                  Subject *
                </label>
                <input
                  type="text"
                  id="subject"
                  name="subject"
                  value={formData.subject}
                  onChange={handleInputChange}
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter subject"
                />
              </div>

              <div>
                <label htmlFor="priority" className="block text-sm font-medium text-gray-700">
                  Priority
                </label>
                <select
                  id="priority"
                  name="priority"
                  value={formData.priority}
                  onChange={handleInputChange}
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="LOW">Low</option>
                  <option value="NORMAL">Normal</option>
                  <option value="HIGH">High</option>
                  <option value="URGENT">Urgent</option>
                </select>
              </div>
            </div>

            {/* Attachments */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm font-medium text-gray-700">
                  Attachments
                </label>
                <button
                  onClick={() => fileInputRef.current?.click()}
                  className="inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                >
                  <Paperclip className="h-4 w-4 mr-2" />
                  Add Files
                </button>
              </div>

              <input
                ref={fileInputRef}
                type="file"
                multiple
                onChange={handleFileSelect}
                className="hidden"
                accept="*/*"
              />

              {attachments.length > 0 && (
                <div className="border border-gray-200 rounded-md p-3 space-y-2">
                  {attachments.map((attachment) => (
                    <div key={attachment.id} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                      <div className="flex items-center space-x-2">
                        <Paperclip className="h-4 w-4 text-gray-400" />
                        <span className="text-sm text-gray-900">{attachment.file.name}</span>
                        <span className="text-xs text-gray-500">({formatFileSize(attachment.file.size)})</span>
                        {attachment.uploading && (
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                        )}
                        {attachment.uploaded && (
                          <CheckCircle className="h-4 w-4 text-green-500" />
                        )}
                        {attachment.error && (
                          <AlertCircle className="h-4 w-4 text-red-500" />
                        )}
                      </div>
                      <button
                        onClick={() => removeAttachment(attachment.id)}
                        className="text-red-600 hover:text-red-800"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Message Body */}
            <div>
              <label htmlFor="body" className="block text-sm font-medium text-gray-700">
                Message *
              </label>
              <textarea
                id="body"
                name="body"
                rows={12}
                value={formData.body}
                onChange={handleInputChange}
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Type your message here..."
              />
            </div>
          </div>

          {/* Footer Actions */}
          <div className="px-6 py-4 border-t border-gray-200 flex justify-between items-center">
            <button
              onClick={() => router.back()}
              className="text-sm text-gray-600 hover:text-gray-800"
            >
              Cancel
            </button>

            <div className="flex space-x-3">
              <button
                onClick={handleSaveDraft}
                disabled={saving}
                className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
              >
                {saving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mr-2"></div>
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Save Draft
                  </>
                )}
              </button>

              <button
                onClick={handleSend}
                disabled={sending}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
              >
                {sending ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Sending...
                  </>
                ) : (
                  <>
                    <Send className="h-4 w-4 mr-2" />
                    Send Email
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
    </StudentLayout>
  )
}
