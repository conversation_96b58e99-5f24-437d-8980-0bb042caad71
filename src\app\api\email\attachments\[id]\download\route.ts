import { NextRequest, NextResponse } from 'next/server'
import { createSecure<PERSON>pi } from '@/lib/secure-api'
import { getAttachmentDownloadUrl } from '@/lib/supabase'
import { prisma } from '@/lib/prisma'

// GET /api/email/attachments/[id]/download - Download email attachment
export const GET = createSecureApi(
  async (context, body, params) => {
    try {
      const { searchParams } = new URL(context.request.url)
      const accountId = searchParams.get('accountId')
      const attachmentId = params?.id

      if (!accountId || !attachmentId) {
        return NextResponse.json(
          { error: 'Account ID and attachment ID are required' },
          { status: 400 }
        )
      }

      // Verify account exists and user has access
      const account = await prisma.emailAccount.findUnique({
        where: { id: accountId, isActive: true }
      })

      if (!account) {
        return NextResponse.json(
          { error: 'Account not found or inactive' },
          { status: 404 }
        )
      }

      // Get attachment details and verify access
      const attachment = await prisma.emailAttachment.findFirst({
        where: {
          id: attachmentId,
          email: {
            OR: [
              // User is the sender
              { senderId: accountId },
              // User is a recipient
              {
                recipients: {
                  some: {
                    accountId,
                    isDeleted: false
                  }
                }
              }
            ]
          }
        },
        include: {
          email: {
            select: {
              id: true,
              subject: true,
              senderId: true
            }
          }
        }
      })

      if (!attachment) {
        return NextResponse.json(
          { error: 'Attachment not found or access denied' },
          { status: 404 }
        )
      }

      // Extract file path from URL for Supabase storage
      const urlParts = attachment.url.split('/')
      const filePath = urlParts.slice(-2).join('/') // emailId/filename

      // Get signed download URL from Supabase
      const { url: downloadUrl, error } = await getAttachmentDownloadUrl(filePath, 3600) // 1 hour expiry

      if (error || !downloadUrl) {
        console.error('Error getting download URL:', error)
        return NextResponse.json(
          { error: 'Failed to generate download URL' },
          { status: 500 }
        )
      }

      // Return download URL or redirect
      const returnUrl = searchParams.get('returnUrl') === 'true'

      if (returnUrl) {
        // Return the URL for client-side handling
        return NextResponse.json({
          success: true,
          downloadUrl,
          filename: attachment.originalName,
          size: attachment.size,
          contentType: attachment.mimeType,
          expiresIn: 3600
        })
      } else {
        // Redirect to the download URL
        return NextResponse.redirect(downloadUrl)
      }

    } catch (error) {
      console.error('Attachment download error:', error)
      return NextResponse.json(
        { error: 'Failed to download attachment' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: true,
    logAudit: false
  }
)

// GET /api/email/attachments/[id] - Get attachment metadata
export const GET_METADATA = createSecureApi(
  async (context, body, params) => {
    try {
      const { searchParams } = new URL(context.request.url)
      const accountId = searchParams.get('accountId')
      const attachmentId = params?.id

      if (!accountId || !attachmentId) {
        return NextResponse.json(
          { error: 'Account ID and attachment ID are required' },
          { status: 400 }
        )
      }

      // Get attachment details and verify access
      const attachment = await prisma.emailAttachment.findFirst({
        where: {
          id: attachmentId,
          email: {
            OR: [
              { senderId: accountId },
              {
                recipients: {
                  some: {
                    accountId,
                    isDeleted: false
                  }
                }
              }
            ]
          }
        },
        include: {
          email: {
            select: {
              id: true,
              subject: true,
              messageId: true,
              sentAt: true
            }
          }
        }
      })

      if (!attachment) {
        return NextResponse.json(
          { error: 'Attachment not found or access denied' },
          { status: 404 }
        )
      }

      return NextResponse.json({
        success: true,
        attachment: {
          id: attachment.id,
          filename: attachment.filename,
          originalName: attachment.originalName,
          contentType: attachment.mimeType,
          size: attachment.size,
          createdAt: attachment.createdAt,
          email: {
            id: attachment.email.id,
            subject: attachment.email.subject,
            messageId: attachment.email.messageId,
            sentAt: attachment.email.sentAt
          },
          downloadUrl: `/api/email/attachments/${attachment.id}/download?accountId=${accountId}`
        }
      })

    } catch (error) {
      console.error('Attachment metadata error:', error)
      return NextResponse.json(
        { error: 'Failed to get attachment metadata' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: true,
    logAudit: false
  }
)

// Handle different endpoints for the same route
export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  const url = new URL(request.url)
  
  if (url.pathname.endsWith('/download')) {
    return GET({ request, clientIP: '', user: null }, null, params)
  } else {
    return GET_METADATA({ request, clientIP: '', user: null }, null, params)
  }
}
