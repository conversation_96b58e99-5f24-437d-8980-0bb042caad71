// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

// User management for admin panel
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  password  String
  role      UserRole @default(EDITOR)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  createdPages       Page[]
  createdMedia       Media[]
  createdContents    Content[]
  createdEmailAccounts EmailAccount[]
  createdStudentAccounts StudentAccount[]
  createdSpamFilters SpamFilter[]
  createdEmailTemplates EmailTemplate[]

  @@map("users")
}

enum UserRole {
  ADMIN
  EDITOR
  VIEWER
}

// Page management
model Page {
  id          String      @id @default(cuid())
  title       String
  slug        String      @unique
  description String?
  content     String?
  metaTitle   String?
  metaDesc    String?
  status      PageStatus  @default(DRAFT)
  parentId    String?
  order       Int         @default(0)
  navigationCategory String? // Navigation dropdown assignment (e.g., "About Us", "Admissions", etc.)
  navigationOrder Int @default(0) // Order within navigation category
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  createdById String

  // Relations
  createdBy User    @relation(fields: [createdById], references: [id])
  parent    Page?   @relation("PageHierarchy", fields: [parentId], references: [id])
  children  Page[]  @relation("PageHierarchy")
  contents  Content[]

  @@map("pages")
}

enum PageStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
}

// Content blocks for flexible page content
model Content {
  id      String      @id @default(cuid())
  type    ContentType
  title   String?
  content String?
  data    String? // JSON data for complex content
  order   Int         @default(0)
  pageId  String
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  createdById String

  // Relations
  page      Page @relation(fields: [pageId], references: [id], onDelete: Cascade)
  createdBy User @relation(fields: [createdById], references: [id])

  @@map("contents")
}

enum ContentType {
  TEXT
  HTML
  IMAGE
  GALLERY
  SLIDESHOW
  TABLE
  LIST
  CONTACT_INFO
  DOWNLOAD
  FEEDBACK_FORM
}

// Media management
model Media {
  id          String       @id @default(cuid())
  filename    String
  originalName String
  mimeType    String
  size        Int
  url         String
  alt         String?
  caption     String?
  category    MediaCategory @default(GENERAL)
  tags        String?      // Comma-separated tags
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
  createdById String

  // Relations
  createdBy User @relation(fields: [createdById], references: [id])

  @@map("media")
}

enum MediaCategory {
  GENERAL
  FACULTY
  INFRASTRUCTURE
  EVENTS
  GALLERY
  DOCUMENTS
  CERTIFICATES
  ACHIEVEMENTS
}

// Contact messages
model ContactMessage {
  id        String   @id @default(cuid())
  name      String
  email     String
  contact   String?
  subject   String?
  message   String
  isRead    Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("contact_messages")
}

// Faculty management
model Faculty {
  id          String   @id @default(cuid())
  name        String
  designation String
  department  String?
  email       String?
  phone       String?
  photoUrl    String?
  bio         String?
  order       Int      @default(0)
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("faculty")
}

// Site settings and configuration
model Setting {
  id    String @id @default(cuid())
  key   String @unique
  value String
  type  SettingType @default(STRING)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("settings")
}

enum SettingType {
  STRING
  NUMBER
  BOOLEAN
  JSON
  COLOR
}

// Navigation menu management
model MenuItem {
  id       String  @id @default(cuid())
  title    String
  url      String?
  pageId   String?
  parentId String?
  order    Int     @default(0)
  isActive Boolean @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  parent   MenuItem?   @relation("MenuHierarchy", fields: [parentId], references: [id])
  children MenuItem[]  @relation("MenuHierarchy")

  @@map("menu_items")
}

// Enhanced Navigation management with full hierarchy support
model NavigationItem {
  id          String  @id @default(cuid())
  title       String
  href        String?
  parentId    String?
  order       Int     @default(0)
  isVisible   Boolean @default(true)
  linkType    String  @default("internal") // internal, external, dropdown
  target      String  @default("_self") // _self, _blank
  description String?
  icon        String?
  cssClass    String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  parent   NavigationItem?   @relation("NavigationHierarchy", fields: [parentId], references: [id], onDelete: Cascade)
  children NavigationItem[]  @relation("NavigationHierarchy")

  @@index([parentId])
  @@index([order])
  @@index([isVisible])
  @@map("navigation_items")
}

// Analytics and tracking
model PageView {
  id        String   @id @default(cuid())
  pageSlug  String
  userAgent String?
  ipAddress String?
  referer   String?
  createdAt DateTime @default(now())

  @@map("page_views")
}

// Email System Models

// Email accounts for both Student IDs and Institute IDs
model EmailAccount {
  id          String      @id @default(cuid())
  email       String      @unique
  password    String      // Hashed password
  displayName String?
  accountType EmailAccountType
  isActive    Boolean     @default(true)

  // Student-specific fields
  studentId   String?     @unique // For Student ID accounts
  rollNumber  String?     // Student roll number
  course      String?     // Student course
  batch       String?     // Student batch year

  // Institute-specific fields
  department  String?     // For Institute ID accounts
  designation String?     // Staff designation

  // Storage and limits
  storageUsed Int         @default(0) // in bytes
  storageLimit Int        @default(**********) // 1GB default

  // Email client settings
  imapEnabled Boolean     @default(true)
  pop3Enabled Boolean     @default(true)
  smtpEnabled Boolean     @default(true)

  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  createdById String

  // Relations
  createdBy   User        @relation(fields: [createdById], references: [id])
  sentEmails  Email[]     @relation("SentEmails")
  receivedEmails EmailRecipient[]
  folders     EmailFolder[]
  sessions    EmailSession[]
  aliases     EmailAlias[]

  @@index([email])
  @@index([accountType])
  @@index([isActive])
  @@index([studentId])
  @@index([department])
  @@map("email_accounts")
}

enum EmailAccountType {
  STUDENT_ID
  INSTITUTE_ID
}

// Email messages
model Email {
  id          String      @id @default(cuid())
  messageId   String      @unique // RFC 5322 Message-ID
  subject     String
  body        String      // HTML content
  bodyText    String?     // Plain text version

  // Sender information
  fromEmail   String
  fromName    String?
  senderId    String

  // Email metadata
  priority    EmailPriority @default(NORMAL)
  isRead      Boolean     @default(false)
  isStarred   Boolean     @default(false)
  isSpam      Boolean     @default(false)
  isDeleted   Boolean     @default(false)
  isDraft     Boolean     @default(false)

  // Threading
  threadId    String?
  inReplyTo   String?     // Message-ID of email being replied to
  references  String?     // Message-IDs of email thread

  // Delivery tracking
  sentAt      DateTime?
  deliveredAt DateTime?
  readAt      DateTime?

  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  sender      EmailAccount @relation("SentEmails", fields: [senderId], references: [id])
  recipients  EmailRecipient[]
  attachments EmailAttachment[]
  queueEntries EmailQueue[]

  @@index([messageId])
  @@index([senderId])
  @@index([fromEmail])
  @@index([sentAt])
  @@index([threadId])
  @@index([isDeleted])
  @@index([isSpam])
  @@map("emails")
}

enum EmailPriority {
  LOW
  NORMAL
  HIGH
  URGENT
}

// Email recipients (To, CC, BCC)
model EmailRecipient {
  id          String      @id @default(cuid())
  emailId     String
  accountId   String
  recipientType RecipientType

  // Status tracking
  isRead      Boolean     @default(false)
  readAt      DateTime?
  isDeleted   Boolean     @default(false)
  deletedAt   DateTime?
  folderId    String?     // Which folder this email is in for this recipient

  createdAt   DateTime    @default(now())

  // Relations
  email       Email       @relation(fields: [emailId], references: [id], onDelete: Cascade)
  account     EmailAccount @relation(fields: [accountId], references: [id])
  folder      EmailFolder? @relation(fields: [folderId], references: [id])

  @@unique([emailId, accountId])
  @@index([accountId])
  @@index([isRead])
  @@index([isDeleted])
  @@index([folderId])
  @@map("email_recipients")
}

enum RecipientType {
  TO
  CC
  BCC
}

// Email attachments
model EmailAttachment {
  id          String   @id @default(cuid())
  emailId     String
  filename    String
  originalName String
  mimeType    String
  size        Int      // in bytes
  url         String   // Storage URL (Supabase storage)

  createdAt   DateTime @default(now())

  // Relations
  email       Email    @relation(fields: [emailId], references: [id], onDelete: Cascade)

  @@map("email_attachments")
}

// Email folders for organization
model EmailFolder {
  id          String      @id @default(cuid())
  accountId   String
  name        String
  folderType  FolderType  @default(CUSTOM)
  parentId    String?
  order       Int         @default(0)
  isSystem    Boolean     @default(false)

  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  account     EmailAccount @relation(fields: [accountId], references: [id], onDelete: Cascade)
  parent      EmailFolder? @relation("FolderHierarchy", fields: [parentId], references: [id])
  children    EmailFolder[] @relation("FolderHierarchy")
  emails      EmailRecipient[]

  @@unique([accountId, name])
  @@map("email_folders")
}

enum FolderType {
  INBOX
  SENT
  DRAFTS
  TRASH
  SPAM
  ARCHIVE
  CUSTOM
}

// Student accounts for portal access
model StudentAccount {
  id          String      @id @default(cuid())
  studentId   String      @unique
  name        String
  email       String      @unique // Personal email for notifications
  phone       String?
  course      String
  batch       String
  rollNumber  String

  // Authentication
  password    String      // Hashed password for student portal
  isActive    Boolean     @default(true)
  lastLogin   DateTime?

  // Institute email access
  instituteEmailId String? @unique // Links to EmailAccount

  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  createdById String

  // Relations
  createdBy   User        @relation(fields: [createdById], references: [id])
  payments    Payment[]

  @@map("student_accounts")
}

// Payment system
model Payment {
  id              String        @id @default(cuid())
  studentId       String
  amount          Float
  currency        String        @default("INR")

  // Payment details
  purpose         String        // Fee type (admission, exam, etc.)
  description     String?
  academicYear    String
  semester        String?

  // Payment gateway info
  gateway         PaymentGateway
  gatewayTxnId    String?       // Transaction ID from gateway
  gatewayOrderId  String?       // Order ID from gateway
  gatewayFee      Float         @default(0) // Additional gateway fee
  gatewayFeePercent Float       @default(0) // Additional fee percentage

  // Status tracking
  status          PaymentStatus @default(PENDING)
  paidAt          DateTime?
  failedAt        DateTime?
  refundedAt      DateTime?

  // Receipt
  receiptNumber   String?       @unique
  receiptUrl      String?       // PDF receipt URL

  // Metadata
  ipAddress       String?
  userAgent       String?

  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  // Relations
  student         StudentAccount @relation(fields: [studentId], references: [id])

  @@map("payments")
}

enum PaymentGateway {
  PAYU
  PHONEPE
  CASHFREE
}

enum PaymentStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  CANCELLED
  REFUNDED
}

// Payment gateway configuration
model PaymentGatewayConfig {
  id              String        @id @default(cuid())
  gateway         PaymentGateway @unique
  isEnabled       Boolean       @default(false)

  // Gateway credentials (encrypted)
  merchantId      String?
  merchantKey     String?
  secretKey       String?
  apiKey          String?

  // Fee configuration
  additionalFeePercent Float    @default(0)
  additionalFeeFixed   Float    @default(0)

  // Environment
  isTestMode      Boolean       @default(true)

  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  @@map("payment_gateway_configs")
}

// Email server configuration
model EmailServerConfig {
  id          String   @id @default(cuid())
  key         String   @unique
  value       String
  description String?
  isEncrypted Boolean  @default(false)

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("email_server_configs")
}

// Spam filtering and rules
model SpamFilter {
  id          String      @id @default(cuid())
  name        String
  description String?
  filterType  FilterType
  pattern     String      // Regex pattern or keyword
  action      FilterAction @default(MARK_SPAM)
  isActive    Boolean     @default(true)
  priority    Int         @default(0)

  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  createdById String

  // Relations
  createdBy   User        @relation(fields: [createdById], references: [id])

  @@map("spam_filters")
}

enum FilterType {
  SUBJECT
  BODY
  SENDER
  DOMAIN
  HEADER
}

enum FilterAction {
  MARK_SPAM
  DELETE
  QUARANTINE
  ALLOW
}

// Email delivery logs
model EmailDeliveryLog {
  id          String        @id @default(cuid())
  emailId     String
  recipientEmail String

  // Delivery status
  status      DeliveryStatus
  attempts    Int           @default(1)
  lastAttempt DateTime      @default(now())
  nextRetry   DateTime?

  // Error details
  errorCode   String?
  errorMessage String?

  // External service tracking
  externalId  String?       // ID from external email service

  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  @@map("email_delivery_logs")
}

enum DeliveryStatus {
  PENDING
  SENT
  DELIVERED
  BOUNCED
  FAILED
  DEFERRED
}

// Email server session management for IMAP/POP3 clients
model EmailSession {
  id          String   @id @default(cuid())
  accountId   String
  sessionType SessionType
  clientInfo  String?  // User agent or client identifier
  ipAddress   String
  isActive    Boolean  @default(true)
  lastActivity DateTime @default(now())
  expiresAt   DateTime

  createdAt   DateTime @default(now())

  // Relations
  account     EmailAccount @relation(fields: [accountId], references: [id], onDelete: Cascade)

  @@index([accountId])
  @@index([isActive])
  @@index([expiresAt])
  @@map("email_sessions")
}

enum SessionType {
  IMAP
  POP3
  SMTP
  WEBMAIL
}

// Email queue for processing outbound emails
model EmailQueue {
  id          String      @id @default(cuid())
  emailId     String
  recipientEmail String
  priority    Int         @default(5) // 1-10, lower is higher priority
  attempts    Int         @default(0)
  maxAttempts Int         @default(3)
  status      QueueStatus @default(PENDING)

  // Scheduling
  scheduledAt DateTime    @default(now())
  processedAt DateTime?
  nextRetry   DateTime?

  // Error tracking
  lastError   String?
  errorCount  Int         @default(0)

  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  email       Email       @relation(fields: [emailId], references: [id], onDelete: Cascade)

  @@index([status])
  @@index([scheduledAt])
  @@index([priority])
  @@index([recipientEmail])
  @@map("email_queue")
}

enum QueueStatus {
  PENDING
  PROCESSING
  SENT
  FAILED
  CANCELLED
}

// Email templates for system emails
model EmailTemplate {
  id          String      @id @default(cuid())
  name        String      @unique
  subject     String
  bodyHtml    String
  bodyText    String?
  templateType TemplateType
  variables   String?     // JSON array of variable names
  isActive    Boolean     @default(true)

  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  createdById String

  // Relations
  createdBy   User        @relation(fields: [createdById], references: [id])

  @@map("email_templates")
}

enum TemplateType {
  WELCOME
  PASSWORD_RESET
  PAYMENT_RECEIPT
  PAYMENT_CONFIRMATION
  ACCOUNT_CREATED
  SYSTEM_NOTIFICATION
}

// Email aliases for forwarding
model EmailAlias {
  id          String   @id @default(cuid())
  alias       String   @unique // The alias email address
  targetEmail String   // Where emails should be forwarded
  accountId   String   // Owner of the alias
  isActive    Boolean  @default(true)

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  account     EmailAccount @relation(fields: [accountId], references: [id], onDelete: Cascade)

  @@index([alias])
  @@index([targetEmail])
  @@map("email_aliases")
}
